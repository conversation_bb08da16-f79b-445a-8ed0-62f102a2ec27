docker build -t rubyhcm/frontend-line:latest .

docker push rubyhcm/frontend-line:latest

kubectl apply -f k8s/
--------
kubectl get pods -n line

kubectl get services -n line

kubectl describe service frontend-service -n line

kubectl port-forward service/frontend-service 30001:80 -n line

minikube service frontend-service -n=line
----------

build docker image with .env => docker build --build-arg VITE_API_BASE_URL=$(grep VITE_API_BASE_URL .env | cut -d '=' -f2) -t frontend .

